# 柴管家第二阶段开发方案 v2.0

## 📋 执行摘要

### 开发策略调整

基于项目实际情况，第二阶段开发采用**后端优先**策略，专注于核心 API 开发和业务逻辑实现。前端将在独立
项目中开发，通过 API 契约驱动协作。

### 核心调整要点

- **开发重心**：100%专注后端 API 开发，暂停前端界面开发
- **首选渠道**：优先集成闲鱼渠道，复用已有连接实现
- **开发方法**：采用用户故事驱动的 BDD 开发方式
- **协作模式**：API 契约驱动的前后端分离开发

## 🎯 项目当前状态分析

### 技术基础设施现状

```mermaid
graph TB
    subgraph "✅ 已完成基础设施"
        A[Docker容器化环境]
        B[PostgreSQL数据库]
        C[Redis缓存系统]
        D[RabbitMQ消息队列]
        E[Elasticsearch搜索引擎]
        F[FastAPI后端框架]
        G[基础监控面板]
    end

    subgraph "🔄 待开发后端核心"
        H[闲鱼平台适配器]
        I[AI服务集成]
        J[消息处理引擎]
        K[知识库管理API]
        L[人机协作API]
        M[用户认证API]
    end

    subgraph "📋 API契约设计"
        N[RESTful API规范]
        O[OpenAPI文档]
        P[API测试套件]
        Q[简单HTML验证页面]
    end

    A --> H
    B --> I
    C --> J
    D --> K
    E --> L
    F --> M

    H --> N
    I --> O
    J --> P
    K --> Q

    style A fill:#c8e6c9
    style B fill:#c8e6c9
    style C fill:#c8e6c9
    style D fill:#c8e6c9
    style E fill:#c8e6c9
    style F fill:#c8e6c9
    style G fill:#c8e6c9
    style H fill:#fff3e0
    style I fill:#fff3e0
    style J fill:#fff3e0
    style K fill:#fff3e0
    style L fill:#fff3e0
    style M fill:#fff3e0
    style N fill:#e3f2fd
    style O fill:#e3f2fd
    style P fill:#e3f2fd
    style Q fill:#e3f2fd
```

### 功能完成度重新评估

| 功能模块      | 后端 API 设计 | 后端实现 | API 测试 | 验证页面 | 优先级 |
| ------------- | ------------- | -------- | -------- | -------- | ------ |
| 用户认证系统  | 90%           | 60%      | 40%      | 需要     | P0     |
| 闲鱼渠道接入  | 80%           | 0%       | 0%       | 需要     | P0     |
| 统一消息处理  | 85%           | 15%      | 10%      | 需要     | P0     |
| AI 副驾式回复 | 75%           | 5%       | 0%       | 需要     | P0     |
| AI 智能托管   | 70%           | 5%       | 0%       | 需要     | P0     |
| 知识库管理    | 65%           | 10%      | 5%       | 需要     | P1     |
| 人机协作机制  | 70%           | 5%       | 0%       | 需要     | P1     |
| 系统监控 API  | 50%           | 30%      | 20%      | 已有     | P2     |

## 🚀 后端优先开发策略

### 开发理念

1. **API First**: 先设计 API 契约，再实现业务逻辑
2. **契约驱动**: 通过 OpenAPI 规范驱动前后端协作
3. **快速验证**: 使用简单 HTML 页面验证 API 功能
4. **迭代开发**: 基于用户故事的增量式开发

### 前后端协作模式

```mermaid
sequenceDiagram
    participant PM as 产品经理
    participant BE as 后端开发
    participant FE as 前端团队
    participant QA as 测试团队

    PM->>BE: 提供用户故事和验收标准
    BE->>BE: 设计API契约(OpenAPI)
    BE->>FE: 共享API契约文档
    BE->>BE: 实现后端API
    BE->>BE: 创建简单HTML验证页面
    BE->>QA: 提供API测试环境
    QA->>BE: API功能验证和反馈
    FE->>BE: 基于契约开发前端(独立项目)
    FE->>BE: API集成测试和问题反馈
```

### 闲鱼渠道优先策略

**选择理由**：

- 已有成熟的连接实现可复用
- 技术风险相对较低
- 业务场景清晰明确
- 可快速验证整体架构

## 📋 基于用户故事的 BDD 开发计划

### 第一优先级 (P0) - 核心 MVP 后端 API

#### 用户故事 1: 闲鱼渠道接入管理

**故事描述**: 作为 IP 运营者，我希望能够接入闲鱼账号到柴管家系统中

**BDD 场景**:

```gherkin
Feature: 闲鱼渠道接入管理
  作为一个IP运营者
  我希望能够接入闲鱼账号
  以便统一管理闲鱼平台的消息

  Background:
    Given 用户已登录柴管家系统
    And 系统支持闲鱼平台接入

  Scenario: 成功接入闲鱼账号
    Given 用户在渠道管理页面
    When 用户选择添加闲鱼渠道
    And 用户输入有效的闲鱼Cookie信息
    And 系统验证Cookie有效性成功
    Then 系统应该返回接入成功状态
    And 渠道列表中应该显示新添加的闲鱼账号
    And 账号状态应该为"已连接"

  Scenario: Cookie验证失败
    Given 用户在渠道管理页面
    When 用户选择添加闲鱼渠道
    And 用户输入无效的Cookie信息
    Then 系统应该返回验证失败错误
    And 错误信息应该包含具体的失败原因
```

**API 设计**:

```yaml
# 闲鱼渠道接入API
POST /api/v1/channels/xianyu
Content-Type: application/json

{
  "name": "我的闲鱼店铺",
  "cookie": "encrypted_cookie_string",
  "config": {
    "auto_reply": false,
    "monitor_keywords": ["咨询", "价格"]
  }
}

# 响应
{
  "success": true,
  "data": {
    "channel_id": "ch_xianyu_001",
    "name": "我的闲鱼店铺",
    "platform": "xianyu",
    "status": "connected",
    "created_at": "2024-08-12T10:00:00Z"
  }
}
```

**开发任务**:

- [ ] 设计闲鱼渠道接入 API 契约
- [ ] 实现 Cookie 验证和存储逻辑
- [ ] 开发渠道状态监控机制
- [ ] 创建 API 测试用例
- [ ] 开发简单 HTML 验证页面

#### 用户故事 2: 闲鱼消息统一处理

**故事描述**: 作为 IP 运营者，我希望在统一界面看到闲鱼平台的消息

**BDD 场景**:

```gherkin
Feature: 闲鱼消息统一处理
  作为一个IP运营者
  我希望能够接收和处理闲鱼消息
  以便统一管理客户咨询

  Scenario: 接收闲鱼新消息
    Given 闲鱼渠道已成功接入
    And 消息监听服务正在运行
    When 闲鱼平台有新的客户咨询消息
    Then 系统应该实时接收到消息
    And 消息应该被标准化处理
    And 消息应该存储到数据库
    And 系统应该触发消息处理流程

  Scenario: 发送回复消息到闲鱼
    Given 用户已选择一个闲鱼会话
    When 用户通过API发送回复消息
    Then 消息应该通过闲鱼渠道发送给客户
    And 系统应该记录发送状态
    And 会话记录应该更新
```

**API 设计**:

```yaml
# 获取消息列表
GET /api/v1/messages?channel_id=ch_xianyu_001&limit=20

# 发送回复消息
POST /api/v1/messages
{
  "channel_id": "ch_xianyu_001",
  "conversation_id": "conv_001",
  "content": "您好，这个商品还有库存",
  "message_type": "text"
}
```

#### 用户故事 3: AI 副驾式回复

**故事描述**: 作为 IP 运营者，我希望 AI 能够分析消息意图并提供回复建议

**BDD 场景**:

```gherkin
Feature: AI副驾式回复
  作为一个IP运营者
  我希望AI能够分析消息并提供回复建议
  以便提高回复效率和质量

  Scenario: AI分析消息意图
    Given 系统接收到新的客户消息
    When AI服务分析消息内容
    Then 系统应该识别出消息意图
    And 意图分析结果应该包含置信度
    And 系统应该生成回复建议

  Scenario: 基于知识库生成回复
    Given AI已识别消息意图为"价格咨询"
    And 知识库中有相关的价格信息
    When AI生成回复建议
    Then 回复内容应该基于知识库信息
    And 回复应该包含置信度评分
```

**API 设计**:

```yaml
# AI意图分析
POST /api/v1/ai/analyze
{
  "message": "这个商品多少钱？",
  "context": {
    "conversation_id": "conv_001",
    "channel_type": "xianyu"
  }
}

# 响应
{
  "intent": "price_inquiry",
  "confidence": 0.95,
  "entities": {
    "product": "商品"
  },
  "suggestions": [
    {
      "content": "这个商品的价格是299元，现在有优惠活动",
      "confidence": 0.88,
      "source": "knowledge_base"
    }
  ]
}
```

### 第二优先级 (P1) - 增强功能 API

#### 用户故事 4: 知识库管理

**故事描述**: 作为 IP 运营者，我希望能够管理知识库内容

#### 用户故事 5: AI 智能托管

**故事描述**: 作为 IP 运营者，我希望 AI 能够自动处理常规对话

#### 用户故事 6: 人机协作机制

**故事描述**: 作为人工客服，我希望能够及时接管 AI 无法处理的对话

## ⏰ 开发时间规划

### 总体时间安排

- **总开发周期**: 12 周 (专注后端开发)
- **MVP 版本**: 第 8 周完成
- **完整后端 API**: 第 12 周完成
- **团队配置**: 3-4 人 (2 后端 + 1AI + 1 测试)

### 详细开发计划

```mermaid
gantt
    title 柴管家第二阶段后端开发时间线
    dateFormat  YYYY-MM-DD
    section 第一阶段 (P0核心API)
    闲鱼渠道接入API    :active, xianyu, 2024-08-12, 3w
    消息处理API       :msg, after xianyu, 2w
    AI服务集成API     :ai, 2024-08-26, 3w
    用户认证API完善    :auth, after msg, 2w

    section 第二阶段 (P1增强API)
    知识库管理API     :kb, after ai, 2w
    人机协作API      :collab, after auth, 2w

    section 第三阶段 (优化完善)
    API性能优化      :perf, after kb, 1w
    文档和测试完善    :docs, after collab, 1w

    section 里程碑
    MVP后端完成      :milestone, mvp, after auth, 0d
    完整后端交付      :milestone, release, after docs, 0d
```

### 关键里程碑

#### 里程碑 1: 闲鱼渠道 MVP (第 5 周)

- 闲鱼渠道接入 API 完成
- 消息收发 API 可用
- 基础 AI 回复 API 实现
- API 文档和测试完善

#### 里程碑 2: 完整 MVP 后端 (第 8 周)

- 所有 P0 功能 API 完成
- 用户认证系统完善
- API 性能达到基本要求
- 简单 HTML 验证页面可用

#### 里程碑 3: 完整后端交付 (第 12 周)

- 所有计划 API 开发完成
- 性能优化和安全加固
- 完整的 API 文档和测试
- 部署和运维文档

## 🔧 API 契约设计和验证方案

### API 设计原则

1. **RESTful 设计**: 遵循 REST 架构风格
2. **版本管理**: 使用 URL 版本控制 (/api/v1/)
3. **统一响应格式**: 标准化的 JSON 响应结构
4. **错误处理**: 详细的错误码和错误信息
5. **安全认证**: JWT Token 认证机制

### 标准响应格式

```json
{
  "success": true,
  "data": {
    // 具体数据内容
  },
  "message": "操作成功",
  "timestamp": "2024-08-12T10:00:00Z",
  "request_id": "req_12345"
}

// 错误响应
{
  "success": false,
  "error": {
    "code": "INVALID_COOKIE",
    "message": "闲鱼Cookie格式无效",
    "details": "Cookie缺少必要的认证字段"
  },
  "timestamp": "2024-08-12T10:00:00Z",
  "request_id": "req_12346"
}
```

### API 验证方案

#### 1. 自动化测试

```python
# pytest测试示例
def test_xianyu_channel_creation():
    """测试闲鱼渠道创建API"""
    response = client.post("/api/v1/channels/xianyu", json={
        "name": "测试店铺",
        "cookie": "valid_cookie_string",
        "config": {"auto_reply": False}
    })

    assert response.status_code == 201
    assert response.json()["success"] is True
    assert "channel_id" in response.json()["data"]
```

#### 2. 简单 HTML 验证页面

```html
<!DOCTYPE html>
<html>
  <head>
    <title>柴管家API验证工具</title>
  </head>
  <body>
    <h1>闲鱼渠道接入测试</h1>
    <form id="channelForm">
      <input type="text" id="channelName" placeholder="渠道名称" required />
      <textarea id="cookieInput" placeholder="闲鱼Cookie" required></textarea>
      <button type="submit">创建渠道</button>
    </form>

    <div id="result"></div>

    <script>
      document.getElementById('channelForm').onsubmit = async e => {
        e.preventDefault();
        const response = await fetch('/api/v1/channels/xianyu', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: document.getElementById('channelName').value,
            cookie: document.getElementById('cookieInput').value,
          }),
        });

        const result = await response.json();
        document.getElementById('result').innerHTML = `<pre>${JSON.stringify(
          result,
          null,
          2
        )}</pre>`;
      };
    </script>
  </body>
</html>
```

#### 3. OpenAPI 文档生成

使用 FastAPI 自动生成的 OpenAPI 文档，提供：

- 完整的 API 接口文档
- 交互式 API 测试界面
- 请求/响应示例
- 错误码说明

## 🧪 BDD 测试策略

### 测试框架选择

- **后端测试**: pytest + pytest-bdd
- **API 测试**: httpx + pytest-asyncio
- **集成测试**: Docker Compose 测试环境
- **性能测试**: Locust

### BDD 测试流程

```python
# features/xianyu_channel.feature 对应的测试实现
from pytest_bdd import scenarios, given, when, then

scenarios('xianyu_channel.feature')

@given('用户已登录柴管家系统')
def user_logged_in(auth_client):
    return auth_client

@when('用户输入有效的闲鱼Cookie信息')
def input_valid_cookie(context):
    context.cookie_data = {
        "name": "测试店铺",
        "cookie": "valid_test_cookie"
    }

@then('系统应该返回接入成功状态')
def verify_success_response(context, response):
    assert response.status_code == 201
    assert response.json()["success"] is True
```

### 测试覆盖目标

- **单元测试覆盖率**: > 85%
- **API 测试覆盖率**: 100% (所有端点)
- **BDD 场景覆盖率**: 100% (所有用户故事)
- **集成测试**: 核心业务流程全覆盖

## ⚠️ 风险评估和应对策略

### 技术风险

#### 🔴 高风险

1. **闲鱼 API 变更风险**

   - 风险: 闲鱼平台 API 或认证机制变更
   - 应对: 建立 API 监控机制，准备多种认证方案

2. **AI 服务稳定性**
   - 风险: AI 模型 API 不稳定或响应慢
   - 应对: 多模型备份，本地缓存，降级机制

#### 🟡 中风险

1. **数据安全合规**

   - 风险: 用户数据处理不当
   - 应对: 数据加密，权限控制，审计日志

2. **系统性能瓶颈**
   - 风险: 高并发下性能下降
   - 应对: 性能测试，缓存优化，异步处理

### 项目风险

#### 🔴 高风险

1. **前后端协作风险**

   - 风险: API 契约理解偏差
   - 应对: 详细文档，定期沟通，原型验证

2. **需求变更风险**
   - 风险: 用户故事频繁变更
   - 应对: 敏捷开发，版本控制，变更评估

## 📈 成功指标和验收标准

### 技术指标

| 指标类别     | 具体指标       | 目标值   | 测量方法   |
| ------------ | -------------- | -------- | ---------- |
| **API 性能** | 平均响应时间   | < 300ms  | 性能监控   |
|              | 并发处理能力   | > 50 QPS | 压力测试   |
| **可靠性**   | API 可用性     | 99.5%    | 监控统计   |
|              | 错误率         | < 1%     | 日志分析   |
| **质量**     | 测试覆盖率     | > 85%    | 自动化测试 |
|              | BDD 场景通过率 | 100%     | BDD 测试   |

### 业务指标

| 指标类别       | 具体指标           | 目标值 | 测量方法 |
| -------------- | ------------------ | ------ | -------- |
| **功能完整性** | 用户故事完成率     | 100%   | 功能测试 |
|                | API 接口完成率     | 100%   | 接口测试 |
| **集成效果**   | 闲鱼消息处理成功率 | > 95%  | 业务监控 |
|                | AI 回复准确率      | > 70%  | 人工评估 |

## 🛠️ 技术实现方案

### 闲鱼平台适配器技术方案

#### 连接实现策略

基于已有项目的成功经验，采用以下技术方案：

```python
# 闲鱼适配器核心实现
class XianyuAdapter(BaseAdapter):
    """闲鱼平台适配器"""

    def __init__(self, channel_config: dict):
        self.cookie = channel_config["cookie"]
        self.session = self._create_session()
        self.message_parser = XianyuMessageParser()

    async def validate_connection(self) -> bool:
        """验证闲鱼连接有效性"""
        try:
            response = await self.session.get(
                "https://2.taobao.com/home/<USER>",
                headers=self._get_headers()
            )
            return "login" not in response.url
        except Exception as e:
            logger.error(f"闲鱼连接验证失败: {e}")
            return False

    async def fetch_messages(self) -> List[Message]:
        """获取新消息"""
        # 实现消息拉取逻辑
        pass

    async def send_message(self, content: str, recipient: str) -> bool:
        """发送消息"""
        # 实现消息发送逻辑
        pass
```

#### Cookie 管理和安全

```python
from cryptography.fernet import Fernet

class CookieManager:
    """Cookie安全管理"""

    def __init__(self, encryption_key: str):
        self.cipher = Fernet(encryption_key.encode())

    def encrypt_cookie(self, cookie: str) -> str:
        """加密存储Cookie"""
        return self.cipher.encrypt(cookie.encode()).decode()

    def decrypt_cookie(self, encrypted_cookie: str) -> str:
        """解密Cookie"""
        return self.cipher.decrypt(encrypted_cookie.encode()).decode()

    def validate_cookie_format(self, cookie: str) -> bool:
        """验证Cookie格式"""
        required_fields = ["_tb_token_", "cookie2", "t"]
        return all(field in cookie for field in required_fields)
```

### AI 服务集成技术方案

#### 多模型路由架构

```python
class AIRouter:
    """AI模型路由器"""

    def __init__(self):
        self.models = {
            "qwen": QwenClient(),
            "gpt4": GPT4Client(),
            "claude": ClaudeClient()
        }
        self.fallback_order = ["qwen", "gpt4", "claude"]

    async def generate_reply(self, message: str, context: dict) -> AIResponse:
        """生成AI回复"""
        for model_name in self.fallback_order:
            try:
                model = self.models[model_name]
                response = await model.generate(message, context)
                if response.confidence > 0.7:
                    return response
            except Exception as e:
                logger.warning(f"模型 {model_name} 调用失败: {e}")
                continue

        # 所有模型都失败，返回默认回复
        return AIResponse(
            content="抱歉，我需要人工客服来帮助您",
            confidence=0.0,
            requires_human=True
        )
```

#### 置信度评估机制

```python
class ConfidenceEvaluator:
    """置信度评估器"""

    def evaluate(self, response: str, context: dict) -> float:
        """评估回复置信度"""
        scores = []

        # 1. 语义相关性评分
        semantic_score = self._calculate_semantic_relevance(response, context)
        scores.append(semantic_score * 0.4)

        # 2. 知识库匹配度评分
        kb_score = self._calculate_knowledge_match(response, context)
        scores.append(kb_score * 0.3)

        # 3. 语言质量评分
        quality_score = self._calculate_language_quality(response)
        scores.append(quality_score * 0.2)

        # 4. 安全性评分
        safety_score = self._calculate_safety_score(response)
        scores.append(safety_score * 0.1)

        return sum(scores)
```

### 消息处理引擎技术方案

#### 消息标准化处理

```python
@dataclass
class StandardMessage:
    """标准化消息格式"""
    id: str
    channel_id: str
    conversation_id: str
    sender_id: str
    content: str
    message_type: MessageType
    timestamp: datetime
    metadata: dict

class MessageProcessor:
    """消息处理器"""

    async def process_incoming_message(self, raw_message: dict, channel: Channel) -> StandardMessage:
        """处理入站消息"""
        # 1. 消息标准化
        std_message = await self._standardize_message(raw_message, channel)

        # 2. 存储消息
        await self.message_repo.save(std_message)

        # 3. 触发AI处理
        if channel.ai_enabled:
            await self._trigger_ai_processing(std_message)

        # 4. 发送实时通知
        await self._send_realtime_notification(std_message)

        return std_message
```

#### 实时消息推送

```python
class MessageNotificationService:
    """消息通知服务"""

    def __init__(self):
        self.websocket_manager = WebSocketManager()
        self.redis_client = get_redis_client()

    async def notify_new_message(self, message: StandardMessage):
        """通知新消息"""
        # 1. WebSocket实时推送
        await self.websocket_manager.broadcast_to_user(
            user_id=message.assignee_id,
            event="new_message",
            data=message.to_dict()
        )

        # 2. Redis发布订阅
        await self.redis_client.publish(
            f"user:{message.assignee_id}:messages",
            message.to_json()
        )
```

## 📚 API 接口规范详细设计

### 核心 API 接口清单

#### 1. 认证相关 API

```yaml
# 用户登录
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}

# 刷新Token
POST /api/v1/auth/refresh
{
  "refresh_token": "refresh_token_string"
}

# 用户信息
GET /api/v1/auth/me
Authorization: Bearer {access_token}
```

#### 2. 渠道管理 API

```yaml
# 创建闲鱼渠道
POST /api/v1/channels/xianyu
{
  "name": "我的闲鱼店铺",
  "cookie": "encrypted_cookie_string",
  "config": {
    "auto_reply": false,
    "monitor_keywords": ["咨询", "价格"],
    "business_hours": {
      "start": "09:00",
      "end": "18:00"
    }
  }
}

# 获取渠道列表
GET /api/v1/channels?status=active&platform=xianyu

# 更新渠道配置
PUT /api/v1/channels/{channel_id}
{
  "config": {
    "auto_reply": true,
    "ai_confidence_threshold": 0.8
  }
}

# 删除渠道
DELETE /api/v1/channels/{channel_id}

# 测试渠道连接
POST /api/v1/channels/{channel_id}/test
```

#### 3. 消息处理 API

```yaml
# 获取消息列表
GET /api/v1/messages?channel_id={id}&limit=20&offset=0&status=unread

# 获取会话列表
GET /api/v1/conversations?channel_id={id}&limit=20

# 获取会话详情
GET /api/v1/conversations/{conversation_id}/messages

# 发送消息
POST /api/v1/messages
{
  "conversation_id": "conv_001",
  "content": "您好，这个商品还有库存",
  "message_type": "text"
}

# 标记消息已读
PUT /api/v1/messages/{message_id}/read

# 消息搜索
GET /api/v1/messages/search?q=价格&channel_id={id}
```

#### 4. AI 服务 API

```yaml
# AI意图分析
POST /api/v1/ai/analyze
{
  "message": "这个商品多少钱？",
  "context": {
    "conversation_id": "conv_001",
    "channel_type": "xianyu",
    "sender_profile": {
      "is_new_customer": true
    }
  }
}

# AI回复生成
POST /api/v1/ai/generate-reply
{
  "message": "这个商品多少钱？",
  "intent": "price_inquiry",
  "context": {
    "conversation_history": [...],
    "product_info": {...}
  }
}

# AI托管设置
PUT /api/v1/conversations/{conversation_id}/ai-mode
{
  "enabled": true,
  "confidence_threshold": 0.8,
  "escalation_rules": {
    "keywords": ["投诉", "退款"],
    "sentiment_threshold": -0.5
  }
}
```

#### 5. 知识库 API

```yaml
# 创建知识条目
POST /api/v1/knowledge/entries
{
  "title": "商品价格说明",
  "content": "我们的商品价格包含...",
  "category": "pricing",
  "keywords": ["价格", "费用", "多少钱"],
  "enabled": true
}

# 搜索知识库
GET /api/v1/knowledge/search?q=价格&category=pricing

# 知识库统计
GET /api/v1/knowledge/stats
```

### API 错误码规范

```yaml
# 标准错误响应格式
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "用户友好的错误信息",
    "details": "技术详细信息",
    "field_errors": {
      "cookie": ["Cookie格式无效"]
    }
  },
  "timestamp": "2024-08-12T10:00:00Z",
  "request_id": "req_12346"
}

# 错误码定义
AUTH_001: "认证失败"
AUTH_002: "Token已过期"
AUTH_003: "权限不足"

CHANNEL_001: "渠道不存在"
CHANNEL_002: "Cookie验证失败"
CHANNEL_003: "渠道连接超时"

MESSAGE_001: "消息发送失败"
MESSAGE_002: "会话不存在"
MESSAGE_003: "消息格式错误"

AI_001: "AI服务不可用"
AI_002: "置信度过低"
AI_003: "内容安全检查失败"
```

## 🔄 开发工作流程

### 用户故事开发流程

```mermaid
flowchart TD
    A[接收用户故事] --> B[编写BDD场景]
    B --> C[设计API契约]
    C --> D[创建API文档]
    D --> E[实现后端逻辑]
    E --> F[编写单元测试]
    F --> G[编写BDD测试]
    G --> H[创建验证页面]
    H --> I[集成测试]
    I --> J{测试通过?}
    J -->|否| K[修复问题]
    K --> I
    J -->|是| L[代码审查]
    L --> M[部署到测试环境]
    M --> N[用户验收测试]
    N --> O{验收通过?}
    O -->|否| P[收集反馈]
    P --> K
    O -->|是| Q[合并到主分支]
```

### 代码质量保证流程

1. **开发阶段**

   - 遵循 TDD/BDD 开发模式
   - 代码覆盖率要求 > 85%
   - 使用类型注解和文档字符串

2. **提交阶段**

   - Pre-commit hooks 检查代码格式
   - 自动运行单元测试
   - 静态代码分析

3. **审查阶段**

   - 强制代码审查
   - API 设计评审
   - 安全性检查

4. **集成阶段**
   - 自动化集成测试
   - 性能基准测试
   - 安全扫描

## 📋 项目交付清单

### MVP 版本交付物 (第 8 周)

- [ ] 闲鱼渠道接入 API (完整实现)
- [ ] 消息收发 API (核心功能)
- [ ] 基础 AI 回复 API (置信度 > 70%)
- [ ] 用户认证 API (安全完善)
- [ ] OpenAPI 文档 (完整准确)
- [ ] 简单 HTML 验证页面 (功能验证)
- [ ] 单元测试套件 (覆盖率 > 85%)
- [ ] BDD 测试场景 (核心用户故事)
- [ ] 部署文档 (开发/测试环境)

### 完整版本交付物 (第 12 周)

- [ ] 所有计划 API 接口 (100%完成)
- [ ] 知识库管理 API (完整功能)
- [ ] 人机协作 API (智能切换)
- [ ] 性能优化 (满足指标要求)
- [ ] 安全加固 (通过安全测试)
- [ ] 完整 API 文档 (包含示例)
- [ ] 集成测试套件 (端到端覆盖)
- [ ] 性能测试报告 (压力测试)
- [ ] 生产部署指南 (运维文档)
- [ ] API 使用指南 (前端集成)

---

**文档版本**: v2.0.0 **创建时间**: 2024 年 8 月 12 日 **更新时间**: 2024 年 8 月 12 日 **负责人**:
Augment Agent **审核状态**: 待审核

> 本文档专注于后端 API 开发，采用用户故事驱动的 BDD 开发方式，通过 API 契约实现前后端分离协作。建议
> 团队严格按照用户故事优先级和 BDD 流程进行开发，确保每个 API 都有完整的测试覆盖和验证机制。
